#!/usr/bin/env python3

import asyncio
import logging
import sys
from discord_bot import run_bot

def main():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bot.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    try:
        print("🚀 Starting MEXC Futures Trading Bot v2.0")
        print("📊 Futures-only optimized version")
        run_bot()
    except KeyboardInterrupt:
        print("\n⏹️ Bot stopped by user")
    except Exception as e:
        logging.error(f"❌ Bot error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
