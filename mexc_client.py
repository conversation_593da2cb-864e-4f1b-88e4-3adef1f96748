#!/usr/bin/env python3

import aiohttp
import asyncio
import hashlib
import hmac
import logging
import random
import time
from typing import Dict, Optional, List
import yaml

_last_api_call_time = 0
_rate_limit_lock = asyncio.Lock()

class MEXCClient:
    
    def __init__(self):
        self.futures_base_url = "https://contract.mexc.com"
        self.session = None
        self.api_key = None
        self.api_secret = None
        self.load_config()

    def load_config(self):
        try:
            with open('config.ymal', 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                mexc_config = config.get('mexc', {})
                self.api_key = mexc_config.get('api_key')
                self.api_secret = mexc_config.get('api_secret')
                logging.info(f"Config loaded - API Key: {'***' + self.api_key[-4:] if self.api_key else 'None'}")
                logging.info(f"Config loaded - API Secret: {'***' + self.api_secret[-4:] if self.api_secret else 'None'}")
        except Exception as e:
            logging.error(f"Error loading config: {e}")

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, *_):
        if self.session:
            await self.session.close()

    def _generate_signature(self, timestamp: str, query_string: str = None) -> str:
        if not self.api_secret:
            return ""

        message = self.api_key + timestamp
        if query_string:
            message += query_string
        else:
            message += ""

        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return signature

    def _get_headers(self, timestamp: str = None) -> Dict[str, str]:
        if timestamp is None:
            timestamp = str(int(time.time() * 1000))
        headers = {
            "ApiKey": self.api_key,
            "Request-Time": timestamp,
            "Content-Type": "application/json"
        }
        return headers

    async def _rate_limit(self):
        global _last_api_call_time, _rate_limit_lock

        async with _rate_limit_lock:
            current_time = time.time()
            time_since_last_call = current_time - _last_api_call_time
            min_interval = random.uniform(1, 3)

            if time_since_last_call < min_interval:
                sleep_time = min_interval - time_since_last_call
                await asyncio.sleep(sleep_time)

            _last_api_call_time = time.time()

    async def _retry_api_call(self, func, *args, max_retries: int = 3, **kwargs):
        for attempt in range(max_retries):
            try:
                result = await func(*args, **kwargs)
                if result is not None:
                    return result
                logging.warning(f"API call returned None, attempt {attempt + 1}/{max_retries}")
            except Exception as e:
                logging.error(f"API call failed on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    raise

            if attempt < max_retries - 1:
                await asyncio.sleep(1)

        return None

    async def _get_price_data_internal(self, symbol: str = "PAXG_USDT") -> Optional[Dict]:
        await self._rate_limit()

        url = f"{self.futures_base_url}/api/v1/contract/ticker"
        params = {"symbol": symbol}

        async with self.session.get(url, params=params) as response:
            response_text = await response.text()
            logging.debug(f"Futures price API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures price API - Response: {response_text}")

            if response.status == 200:
                try:
                    data = await response.json()
                    if data.get('success') and data.get('data'):
                        return self._format_price_data(data['data'], symbol)
                    else:
                        logging.error(f"Futures price API returned no data: {data}")
                        return None
                except Exception as e:
                    logging.error(f"Error parsing futures price response: {e}")
                    return None
            else:
                logging.error(f"Error getting futures price data for {symbol}: {response.status}, Response: {response_text}")
                return None

    async def get_price_data(self, symbol: str = "PAXG_USDT") -> Optional[Dict]:
        return await self._retry_api_call(self._get_price_data_internal, symbol)

    def _format_price_data(self, ticker_data: Dict, symbol: str = "PAXG_USDT") -> Dict:
        def safe_float(value, default=0.0):
            try:
                return float(value) if value else default
            except (ValueError, TypeError):
                return default

        current_price = safe_float(ticker_data.get("lastPrice"))
        price_change_24h = safe_float(ticker_data.get("riseFallValue"))
        price_change_percent_24h = safe_float(ticker_data.get("riseFallRate")) * 100

        volume_contracts = safe_float(ticker_data.get("volume24"))
        if symbol == "PAXG_USDT":
            volume_display = volume_contracts / 1000.0
        else:
            volume_display = volume_contracts * 0.001

        return {
            "symbol": symbol,
            "current_price": current_price,
            "price_change_24h": price_change_24h,
            "price_change_percent_24h": price_change_percent_24h,
            "high_price": safe_float(ticker_data.get("high24Price")),
            "low_price": safe_float(ticker_data.get("lower24Price")),
            "volume": volume_display
        }

    async def _get_futures_positions_internal(self, symbol: str = None) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            logging.error("API credentials missing for futures positions")
            return []

        await self._rate_limit()

        url = f"{self.futures_base_url}/api/v1/private/position/open_positions"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, query_string if query_string else None)

        headers = self._get_headers(timestamp)
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.info(f"Futures positions API - Status: {response.status}, URL: {url}")
            logging.info(f"Futures positions API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    logging.info(f"Futures positions API - Full result: {result}")
                    positions_data = result.get('data', []) if result.get('success') else []
                    logging.info(f"Futures positions API - Parsed data: {positions_data}")
                    return positions_data
                except Exception as e:
                    logging.error(f"Error parsing futures positions response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures positions: {response.status}, Response: {response_text}")
                return None

    async def get_futures_positions(self, symbol: str = None) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_positions_internal, symbol)
        return result if result is not None else []

    async def _get_futures_account_assets_internal(self) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            logging.error("API credentials missing for futures account assets")
            return []

        await self._rate_limit()

        url = f"{self.futures_base_url}/api/v1/private/account/assets"
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp)

        headers = self._get_headers(timestamp)
        headers["Signature"] = signature

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.info(f"Futures account assets API - Status: {response.status}, URL: {url}")
            logging.info(f"Futures account assets API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    logging.info(f"Futures account assets API - Full result: {result}")
                    assets_data = result.get('data', []) if result.get('success') else []
                    logging.info(f"Futures account assets API - Parsed data: {assets_data}")
                    return assets_data
                except Exception as e:
                    logging.error(f"Error parsing futures account assets response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures account assets: {response.status}, Response: {response_text}")
                return None

    async def get_futures_account_assets(self) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_account_assets_internal)
        return result if result is not None else []

    async def _get_futures_open_orders_internal(self, symbol: str = None) -> Optional[List]:
        if not self.api_key or not self.api_secret:
            logging.error("API credentials missing for futures open orders")
            return []

        await self._rate_limit()

        url = f"{self.futures_base_url}/api/v1/private/order/list/open_orders"

        data_original = {}
        if symbol:
            data_original["symbol"] = symbol

        query_string = '&'.join(f'{k}={v}' for k, v in sorted(data_original.items()))
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, query_string if query_string else None)

        headers = self._get_headers(timestamp)
        headers["Signature"] = signature

        if query_string:
            url = f"{url}?{query_string}"

        async with self.session.get(url, headers=headers) as response:
            response_text = await response.text()
            logging.debug(f"Futures open orders API - Status: {response.status}, URL: {url}")
            logging.debug(f"Futures open orders API - Response: {response_text}")

            if response.status == 200:
                try:
                    result = await response.json()
                    orders_data = result.get('data', []) if result.get('success') else []
                    logging.debug(f"Futures open orders API - Parsed data: {len(orders_data) if orders_data else 0} orders")
                    return orders_data
                except Exception as e:
                    logging.error(f"Error parsing futures open orders response: {e}")
                    return []
            else:
                logging.error(f"Error getting futures open orders: {response.status}, Response: {response_text}")
                return None

    async def get_futures_open_orders(self, symbol: str = None) -> Optional[List]:
        result = await self._retry_api_call(self._get_futures_open_orders_internal, symbol)
        return result if result is not None else []
